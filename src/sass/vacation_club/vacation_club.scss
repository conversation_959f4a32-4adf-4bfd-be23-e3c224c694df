.toggle_vacation_club_search {
    position: relative;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 16px;

    .vacation_club_search_label {
        font-size: 16px;
        letter-spacing: 0.45px;
    }

    .vacation_club_switcher {
        position: relative;
        width: 68px;
        height: 33px;
        overflow: visible;
        border-radius: 100px;

        .vacation_club_search {
            position: relative;
            width: 100%;
            height: 100%;
            padding: 0;
            margin: 0;
            opacity: 0;
            cursor: pointer;
            z-index: 3;

            &:checked:active+.knobs:before {
                margin-left: -26px;
            }

            &:checked+.knobs:before {
                content: "\f00c";
                right: 3px;
                background-color: #F28E2A;
            }

            &:checked~.layer {
                background-color: transparent;
            }
        }

        .knobs,
        .layer {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
        }

        .knobs {
            z-index: 2;

            &:before {
                content: "\f00d";
                font-family: "Font Awesome 5 Pro";
                position: absolute;
                top: 3px;
                left: auto;
                right: 37px;
                width: 27px;
                height: 27px;
                box-sizing: border-box;
                color: white;
                background-color: #92714f;
                font-size: 14px;
                font-weight: bold;
                text-align: center;
                line-height: 0.7;
                padding: 11px 4px;
                border-radius: 50%;
                transition: 0.3s ease all, left 0.3s cubic-bezier(0.18, 0.89, 0.35, 1.15);
            }
        }

        .layer {
            width: 100%;
            background-color: rgba(#92714f, 0.2);
            border: 1px solid #F28E2A;
            transition: 0.3s ease all;
            z-index: 1;
            border-radius: 100px;
        }
    }
}

.vacation_club_container {
    display: none;
    padding: 20px 30px 0 30px;

    &.active {
        display: block;
    }

    .vacation_club_form {
        display: flex;
        align-items: center;

        .inputs_wrapper {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            width: 800px;

            .input_wrapper {
                position: relative;
                border-radius: 4px !important;
                border: 1px solid #D7DAE2;
                background-color: white;
                box-shadow: 0 2px 3px rgba(0, 0, 0, 0.05);
                margin: 10px 0;
                width: calc((100% - 40px) / 3);
                min-height: 40px;

                label {
                    display: inline-block;
                    position: absolute;
                    top: 50%;
                    left: 0;
                    transform: translateY(-50%);
                    margin-left: 16px;
                    padding: 0 4px;
                    background-color: white;
                    font-size: 16px;
                    white-space: nowrap;
                    pointer-events: none;
                    transition: all 0.3s;

                    &.error {
                        transform: none;
                        background: transparent;
                        margin-left: 0;
                        margin-top: 10px;
                        top: initial !important;
                        font-size: 16px !important;
                        bottom: -30px;
                        font-weight: 300;
                        color: red;
                    }
                }

                input,
                select {
                    width: 100%;
                    height: 100%;
                    border: none;
                    background: none;
                    outline: none;
                    padding: 0 20px;
                    font-family: 'Source Sans Pro', sans-serif;
                    font-size: 16px;
                }

                select {
                    width: 95%;
                }

                &.active label:not(.error) {
                    top: 0;
                    font-size: 12px;
                }
            }
        }

        .buttons_block {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            align-items: stretch;
            width: 180px;
            margin-left: 100px;

            .search_loader {
                width: 215px;
                text-align: center;
                display: none;

                img {
                    width: 50px;
                }
            }

            div:not(.search_loader) {
                width: 100%;
                height: 40px;
                border: 1px solid #F28E2A;
                border-radius: 4px !important;
                background-color: white;
                margin: 10px 0;
                font-size: 18px;
                letter-spacing: 0.45px;
                color: #F28E2A;
                transition: opacity 0.3s;

                &.find_user_btn {
                    background-color: #F28E2A;
                    color: white;
                }

                &:hover {
                    opacity: 0.7;
                }

                button {
                    appearance: none;
                    width: 100%;
                    height: 100%;
                    border: none;
                    background: none;
                    font: inherit;
                    color: inherit;
                    cursor: pointer;
                }

                &.hide {
                    display: none;
                }
            }
        }
    }

    .vacation_club_results {
        margin-top: 10px;
        border-bottom: 1px solid #D7DAE2;
        padding: 10px 0;

        .user_element {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border: 1px solid #D7DAE2;
            border-radius: 12px !important;
            margin-bottom: 10px;

            .user_info {
                width: calc(100% - 400px);
                display: flex;
                flex-wrap: wrap;
                padding: 10px 30px;

                .field_element {
                    width: calc(100%/3);
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding-right: 30px;
                    margin: 7px 0;

                    input,
                    select {
                        width: calc(100% - 40px);
                        border: none;
                        background: none !important;
                        outline: none;
                        appearance: none;
                        pointer-events: none;
                    }

                    label {
                        display: none !important;
                    }
                }
            }

            .buttons_wrapper {
                width: 400px;
                padding: 0 30px;
                display: flex;
                align-items: center;
                justify-content: space-between;

                div {
                    width: calc(50% - 5px);
                    height: 40px;
                    border: 1px solid #43425D;
                    border-radius: 4px !important;
                    background-color: white;
                    margin: 15px 0;
                    font-size: 18px;
                    letter-spacing: 0.45px;
                    color: #43425D;
                    transition: opacity 0.3s;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    cursor: pointer;

                    &.select_user_btn,
                    &.cancel_user_btn {
                        background-color: #43425D;
                        color: white;
                    }

                    &:hover {
                        opacity: 0.7;
                    }
                }

                .cancel_user_btn {
                    display: none;
                }
            }

            &.vacation_club_user_template {
                display: none;
            }

            &.selected {
                .buttons_wrapper {
                    .cancel_user_btn {
                        display: flex;
                    }

                    .select_user_btn {
                        display: none;
                    }
                }
            }
        }

        .pagination {
            display: none;
            justify-content: center;
            align-items: center;
            font-size: 20px;
            margin-top: 20px;

            .pages {
                display: flex;
                justify-content: center;
                align-items: center;
                margin: 0 10px;

                .page_button {
                    margin: 0 10px;
                    cursor: pointer;

                    &.active {
                        color: #F28E2A;
                    }
                }
            }

            i {
                font-size: 24px;
                margin-top: 3px;
                cursor: pointer;
            }

            &.active {
                display: flex;
            }
        }
    }
}