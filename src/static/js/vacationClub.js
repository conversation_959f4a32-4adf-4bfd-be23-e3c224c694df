const VacationClubController = function () {
    return {
        config: {
            clientActiveClass: '.tab_client_content.active',
            hotelActiveClass: '.tab_client_content.active .hoteltabs-content .hoteltab.active',
            vacationClubClass: '.vacation_club_container',
            adapterUrl: '',
            selectedClients: {},
            users: {},
            usersPerPage: 10,
        },

        init: function () {
            $('body').on('shown.bs.tab', '.hoteltabs-container.nav.nav-tabs', this.requestVacationClubInfoToActiveHotel);

            const vacationClubContainer = $(VacationClubController.config.vacationClubClass);
            const formInputs = vacationClubContainer.find('.inputs_wrapper input');
            formInputs.on('focus', function () {
                $(this).closest('.input_wrapper').addClass('active');
            });
            formInputs.on('change', VacationClubController.checkInputs);
            formInputs.on('blur', VacationClubController.checkInputs);

            vacationClubContainer.find('.find_user_btn').click(this.handleSearchUser);
            vacationClubContainer.find('.clear_search_btn').click(this.handleClearSearch);
            vacationClubContainer.find('.select_user_btn').click(this.handleSelectUser);
            vacationClubContainer.find('.cancel_user_btn').click(this.handleCancelSelectedUser);
            vacationClubContainer.find('.pagination .prev-page').click(() => this.handlePageNavigation('prev'));
            vacationClubContainer.find('.pagination .next-page').click(() => this.handlePageNavigation('next'));

            $('.toggle_vacation_club_search input').on('change', function () {
                const clientTab = VacationClubController.getClientTabActive();
                VacationClubController.toggleVacationClubContainer(clientTab, $(this).is(':checked'));
            });

            $(document).on("click", ".vacation_club_switcher", function (e) {
                const checkbox = $(this).find("input[type='checkbox']");

                if (!$(e.target).is('input, label')) {
                    VacationClubController.setCheckboxState(checkbox, !checkbox.prop("checked"));
                }
            });

            window.addEventListener('message', function (event) {
                const actions = {
                    'vacation_club_info': VacationClubController.toggleVacationClubActivation
                };

                actions[event.data.message] && actions[event.data.message](event);
            }, false);
        },

        getActiveHotelTab: function () {
            return $(this.config.hotelActiveClass);
        },

        getClientTabActive: function () {
            return $(this.config.clientActiveClass);
        },

        toggleVacationClubActivation: function (event) {
            VacationClubController.config.adapterUrl = event.data.adapter_url;
            const iframeSender = $(event.source.frameElement);
            const clientTab = iframeSender.closest('.tab_client_content');
            VacationClubController.toggleVacationClubToggle(clientTab, event.data.vacation_club_enabled);

            if (event.data.vacation_club_enabled && event.data.first_load) {
                VacationClubController.sendActiveClientIdHotelIframe(iframeSender);
            }
        },

        toggleVacationClubToggle: function (clientTab, vacation_club_enabled) {
            const toggle = clientTab.find('.toggle_vacation_club_search');
            const checkbox = toggle.find('input');

            if (VacationClubController.getAllActiveForms(clientTab).length) {
                // If there are multiple registration forms actives we show the toggle but inactive
                vacation_club_enabled ? toggle.show() : toggle.hide();
                VacationClubController.setCheckboxState(checkbox, false);
            } else {
                // If there is no forms active we hide the toggle and set the state denpending on the vacation club enabled state.
                // The toggle is hidden because if only one form is active the toggle is not needed.
                toggle.hide();
                VacationClubController.setCheckboxState(checkbox, vacation_club_enabled);
            }
        },

        setCheckboxState: function (checkbox, checked) {
            checkbox.prop('checked', checked);
            checkbox[0].offsetHeight;
            checkbox.trigger('change');
        },

        toggleVacationClubContainer: function (clientTab, active) {
            active ?
                VacationClubController.showVacationClubContainer(clientTab) :
                VacationClubController.hideVacationClubContainer(clientTab);
        },

        showVacationClubContainer: function (clientTab) {
            // Hide the other forms
            VacationClubController.getAllActiveForms(clientTab).each(function () {
                $(this).attr('data-enabled', true);
                $(this).removeClass('active');
            });

            // Show the vacation club container
            clientTab.find(VacationClubController.config.vacationClubClass).addClass('active');
        },

        hideVacationClubContainer: function (clientTab) {
            // Show the other forms
            clientTab.find('.registration_forms [data-enabled="true"]').each(function () {
                $(this).addClass('active');
            });

            // Hide the vacation club container
            clientTab.find(VacationClubController.config.vacationClubClass).removeClass('active');
        },

        getAllActiveForms: function (clientTab) {
            return clientTab.find('.registration_forms .active').filter(function () {
              return Array.from(this.classList).some(c => c.endsWith('_container'));
            });
        },

        requestVacationClubInfoToActiveHotel: function () {
            const hotelTab = VacationClubController.getActiveHotelTab();
            const message = {
                message: "request_vacation_club_info"
            };

            hotelTab.find('.booking_engine_iframe_wrapper iframe')[0].contentWindow.postMessage(message, '*');
        },

        sendActiveClientIdHotelIframe: function (hotelIframe) {
            const clientTab = hotelIframe.closest('.tab_client_content');
            const client = VacationClubController.config.selectedClients[clientTab.attr('id')];
            if (!client) {
                return;
            }
            const message = {
                message: "vacation_club_client_selected",
                id: client.id,
                email: client.email
            };

            hotelIframe[0].contentWindow.postMessage(message, '*');
        },

        handleSearchUser: async function () {
            const $button = $(this);
            VacationClubController.toggleLoader($button, true);

            try {
                const clientTab = VacationClubController.getClientTabActive();
                const userInfo = VacationClubController.getUserInfo(clientTab.find('.vacation_club_form'));
                const users = await VacationClubController.searchUsers(userInfo);

                VacationClubController.config.users[clientTab.attr('id')] = users;

                VacationClubController.createPagination(clientTab, users);
                const usersToCreate = users.slice(0, VacationClubController.config.usersPerPage);
                VacationClubController.createUserElements(clientTab, usersToCreate);
            } catch (error) {
                console.error('Error searching users:', error);
                alert('Error al buscar usuarios. Por favor, inténtelo de nuevo.');
            } finally {
                VacationClubController.toggleLoader($button, false);
            }
        },

        handleClearSearch: function () {
            $(this).closest('.vacation_club_form').find('.input_wrapper').removeClass('active');
            const vacationClubContainer = $(this).closest(VacationClubController.config.vacationClubClass);
            // Remove all user elements except the base template
            vacationClubContainer.find('.user_element').not('.vacation_club_user_template').remove();
            vacationClubContainer.find('.pagination').removeClass('active');

            VacationClubController.handleCancelSelectedUser();
        },

        handleSelectUser: function () {
            const questionMessage = 'Se asociará la pestaña de cliente al usuario y se reiniciarán las búsquedas realizadas \n\n¿Desea continuar?';
            if (!window.confirm(questionMessage)) {
                return;
            }

            const userSelected = $(this).closest('.user_element');
            $(this).closest('.vacation_club_results').find('.user_element').removeClass('selected');
            $(this).closest('.user_element').addClass('selected');

            const userData = {
                id: userSelected.find('.user_field[name="member_id"]').val(),
                email: userSelected.find('.user_field[name="email"]').val()
            };
            const message = {
                message: "vacation_club_client_selected",
                ...userData
            };

            const clientTab = VacationClubController.getClientTabActive();
            clientTab.find('.booking_engine_iframe_wrapper iframe').each(function () {
                this.contentWindow.postMessage(message, '*');
            });

            VacationClubController.config.selectedClients[clientTab.attr('id')] = userData;
        },

        handleCancelSelectedUser: function () {
            const clientTab = VacationClubController.getClientTabActive();
            clientTab.find('.vacation_club_container .user_element.selected').removeClass('selected');

            const message = {
                message: "vacation_club_client_cancel"
            };

            clientTab.find('.booking_engine_iframe_wrapper iframe').each(function () {
                this.contentWindow.postMessage(message, '*');
            });

            VacationClubController.config.selectedClients[clientTab.attr('id')] = null;
        },

        getUserInfo: function (form) {
            return form.find('.user_field').toArray().reduce((userInfo, field) => {
                if (field.value) {
                    userInfo[field.name] = field.value;
                }

                return userInfo;
            }, {});
        },

        searchUsers: async function (data) {
            if (!data || $.isEmptyObject(data)) {
                return [];
            }

            try {
                const queryParams = new URLSearchParams();
                for (const [key, value] of Object.entries(data)) {
                    if (value) {
                        queryParams.append(key, value);
                    }
                }

                const baseEndpoint = `${VacationClubController.config.adapterUrl}/contracts`;
                const params = queryParams.toString();

                if (!params) {
                    return [];
                }

                const endpoint = `${baseEndpoint}?${queryParams.toString()}`;

                const options = {
                    method: 'GET',
                    mode: 'cors',
                    headers: {'Content-Type': 'application/json'}
                };

                return await VacationClubController.sendRequest(endpoint, data, options);
            } catch (error) {
                console.error('Error in searchUsers:', error);
                throw new Error('Failed to search users: ' + error.message);
            }
        },

        sendRequest: async function (endpoint, data, options) {
            try {
                const response = await fetch(endpoint, options);

                // Check if the response is ok (status 200-299)
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}`);
                }

                // Check content type and parse accordingly
                const contentType = response.headers.get("Content-Type");
                if (contentType && contentType.includes('application/json')) {
                    return await response.json();
                }

                return response;
            } catch (error) {
                // Handle network errors, parsing errors, and HTTP errors
                if (error instanceof TypeError) {
                    throw new Error('Network error: Unable to connect to the server');
                } else if (error.name === 'SyntaxError') {
                    throw new Error('Invalid JSON response from server');
                } else {
                    throw error; // Re-throw other errors (including our custom HTTP errors)
                }
            }
        },

        handlePageNavigation: function (action) {
            const clientTab = VacationClubController.getClientTabActive();
            const paginationContainer = clientTab.find(`.vacation_club_container .pagination`);
            const activePage = paginationContainer.find('.page_button.active');
            const pageNumber = +activePage.data('page');
            const totalPages = paginationContainer.find('.page_button').length;

            if (action === 'prev' && pageNumber > 1) {
                VacationClubController.changePage(clientTab, pageNumber - 1);
            } else if (action === 'next' && pageNumber < totalPages) {
                VacationClubController.changePage(clientTab, pageNumber + 1);
            }
        },

        changePage: function (clientTab, pageNumber) {
            const paginationContainer = clientTab.find(`.vacation_club_container .pagination`);
            paginationContainer.find('.page_button').removeClass('active');
            paginationContainer.find(`.page_button[data-page="${pageNumber}"]`).addClass('active');

            const users = VacationClubController.config.users[clientTab.attr('id')];
            const usersToCreate = users.slice((pageNumber - 1) * VacationClubController.config.usersPerPage, pageNumber * VacationClubController.config.usersPerPage);
            VacationClubController.createUserElements(clientTab, usersToCreate);
        },

        createPagination: function (clientTab, users) {
            const totalPages = Math.ceil(users.length / VacationClubController.config.usersPerPage);
            const paginationContainer = clientTab.find(`.vacation_club_container .pagination`);
            paginationContainer.find('.pages').empty();

            if (totalPages > 1) {
                for (let i = 1; i <= totalPages; i++) {
                    const pageButton = $(`<span class="page_button ${i === 1 ? 'active' : ''}" data-page="${i}">${i}</span>`);
                    pageButton.click(() => VacationClubController.changePage(clientTab, i));
                    paginationContainer.find('.pages').append(pageButton);
                }

                paginationContainer.addClass('active');
            } else {
                paginationContainer.removeClass('active');
            }
        },

        createUserElements: function (clientTab, users) {
            const userTemplate = clientTab.find('.vacation_club_user_template');
            const userContainer = clientTab.find('.vacation_club_results');

            const userElements = users.map(user => {
                const userElement = userTemplate.clone(true, true);
                userElement.removeClass('vacation_club_user_template');

                Object.entries(user).forEach(([key, value]) => {
                    userElement.find(`.user_${key} .user_field`).val(value);
                });

                return userElement;
            });

            userContainer.find('.user_element').not('.vacation_club_user_template').remove();
            userContainer.prepend(userElements);
        },

        toggleLoader: function (btn, show) {
            if (show) {
                btn.addClass('hide');
                $('.search_loader').show();
            } else {
                btn.removeClass('hide');
                $('.search_loader').hide();
            }
        },

        checkInputs: function () {
            const target_wrapper = $(this).closest('.input_wrapper');
            if ($(this).val()) {
                target_wrapper.addClass('active');
            } else {
                target_wrapper.removeClass('active');
            }
        }
    };
}();
